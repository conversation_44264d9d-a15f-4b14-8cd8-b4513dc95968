:root {
    /* Premium Color Palette */
    --primary-color: #1a365d;        /* Deep Navy Blue */
    --primary-light: #2d5a87;        /* Lighter Navy */
    --primary-dark: #0f2a44;         /* Darker Navy */
    --secondary-color: #d4af37;      /* Premium Gold */
    --secondary-light: #f4d03f;      /* Light Gold */
    --secondary-dark: #b8941f;       /* Dark Gold */
    --accent-color: #e74c3c;         /* Construction Red */
    --accent-light: #ec7063;         /* Light Red */

    /* Neutral Colors */
    --dark-color: #2c3e50;           /* Charcoal */
    --dark-light: #34495e;           /* Light Charcoal */
    --light-color: #f8f9fa;          /* Off White */
    --white-color: #ffffff;          /* Pure White */
    --black-color: #1a1a1a;          /* Rich Black */
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* Premium Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
    --gradient-hero: linear-gradient(135deg, rgba(26, 54, 93, 0.95) 0%, rgba(45, 90, 135, 0.85) 100%);
    --gradient-overlay: linear-gradient(180deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 100%);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.25);
    --shadow-premium: 0 10px 30px rgba(26, 54, 93, 0.3);

    /* Typography */
    --font-primary: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
    --font-secondary: 'Playfair Display', Georgia, serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    --spacing-3xl: 6rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Import Premium Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.7;
    margin: 0;
    padding: 0;
    background: var(--light-color);
    color: var(--dark-color);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Premium Typography Scale */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--dark-color);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    letter-spacing: -0.02em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 600;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 600;
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 500;
}

p {
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--gray-700);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-normal);
}

a:hover {
    color: var(--primary-light);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-primary { font-family: var(--font-primary); }
.font-secondary { font-family: var(--font-secondary); }

.fw-light { font-weight: 300; }
.fw-normal { font-weight: 400; }
.fw-medium { font-weight: 500; }
.fw-semibold { font-weight: 600; }
.fw-bold { font-weight: 700; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-white { color: var(--white-color); }
.text-dark { color: var(--dark-color); }

/* Layout & Container System */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    width: 100%;
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-lg);
}

.container-sm {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.container-lg {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Section Spacing */
.section {
    padding: var(--spacing-3xl) 0;
}

.section-sm {
    padding: var(--spacing-2xl) 0;
}

.section-lg {
    padding: calc(var(--spacing-3xl) * 1.5) 0;
}

/* Premium Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Flexbox Utilities */
.flex {
    display: flex;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex-column {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

/* Premium Card Component */
.card {
    background: var(--white-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-premium);
}

.card-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--gray-200);
}

.card-body {
    margin-bottom: var(--spacing-lg);
}

.card-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--gray-200);
}

/* Premium Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: var(--dark-color);
    padding: var(--spacing-md) 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    padding: var(--spacing-sm) 0;
    box-shadow: var(--shadow-lg);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: var(--transition-normal);
}

.navbar .logo:hover {
    transform: scale(1.05);
}

.logo-img {
    height: 45px;
    width: auto;
    object-fit: contain;
}

.nav-links {
    list-style: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-links li {
    position: relative;
}

.nav-links a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-links a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-secondary);
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.nav-links a:hover {
    color: var(--primary-color);
    background: rgba(26, 54, 93, 0.05);
}

.nav-links a:hover::before {
    width: 80%;
}

.nav-links a.active {
    color: var(--primary-color);
    background: rgba(26, 54, 93, 0.1);
}

.nav-links a.active::before {
    width: 80%;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.menu-toggle:hover {
    background: rgba(26, 54, 93, 0.1);
}

.menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: var(--transition-normal);
    border-radius: 2px;
}

.menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Premium Hero Section */
.hero {
    position: relative;
    background: var(--gradient-hero), url('../images/hero-bg.jpg') no-repeat center center/cover;
    color: var(--white-color);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    animation: fadeInUp 1s ease-out;
}

.hero-content h1,
.hero-title {
    font-family: var(--font-secondary);
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
    letter-spacing: -0.02em;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    color: var(--white-color);
}

.hero-content .hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    font-weight: 300;
    margin-bottom: var(--spacing-sm);
    color: #87ceeb;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.hero-content p,
.hero-description {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    font-weight: 400;
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
    opacity: 0.95;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    color: #87ceeb;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--spacing-2xl);
}

/* Animated Background Elements */
.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.hero-bg-elements::before,
.hero-bg-elements::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(212, 175, 55, 0.1);
    animation: float 6s ease-in-out infinite;
}

.hero-bg-elements::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.hero-bg-elements::after {
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    animation: bounce 2s infinite;
}

.scroll-indicator i {
    font-size: 2rem;
    color: var(--white-color);
    opacity: 0.8;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Premium Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    text-decoration: none;
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: 1rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-width: 160px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white-color);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--dark-color);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--secondary-light) 0%, var(--secondary-color) 100%);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: none;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-ghost {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white-color);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-white {
    background: var(--white-color);
    color: var(--primary-color);
    border: 2px solid var(--white-color);
    box-shadow: var(--shadow-md);
}

.btn-white:hover {
    background: var(--primary-color);
    color: var(--white-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.1rem;
    min-width: 160px;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.9rem;
    min-width: 120px;
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: var(--shadow-md);
}

/* Button Icons */
.btn i {
    font-size: 1.1em;
}

.btn-icon-right i {
    order: 2;
}

/* Loading State */
.btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scroll Animations */
.service-card,
.company-content,
.company-image,
.stat-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-card.animate-in,
.company-content.animate-in,
.company-image.animate-in,
.stat-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.service-card:nth-child(1) { transition-delay: 0.1s; }
.service-card:nth-child(2) { transition-delay: 0.2s; }
.service-card:nth-child(3) { transition-delay: 0.3s; }

.stat-item:nth-child(1) { transition-delay: 0.1s; }
.stat-item:nth-child(2) { transition-delay: 0.2s; }
.stat-item:nth-child(3) { transition-delay: 0.3s; }

/* Navbar Transition */
.navbar {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced Hover Effects */
.service-card {
    position: relative;
    overflow: hidden;
}

.service-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: var(--transition-slow);
}

.service-card:hover::after {
    left: 100%;
}

/* Pulse Animation for Icons */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(26, 54, 93, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(26, 54, 93, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(26, 54, 93, 0);
    }
}

.service-icon {
    animation: pulse 2s infinite;
}

.service-card:hover .service-icon {
    animation: none;
}

/* Section Headers */
.section-header {
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Overview Section */
.services-overview {
    background: var(--white-color);
}

.service-card {
    background: var(--white-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-premium);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
}

.service-icon i {
    font-size: 2rem;
    color: var(--white-color);
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.service-card p {
    margin-bottom: var(--spacing-lg);
    color: var(--gray-600);
}

.service-features {
    list-style: none;
    padding: 0;
    text-align: left;
}

.service-features li {
    padding: var(--spacing-xs) 0;
    color: var(--gray-700);
    position: relative;
    padding-left: var(--spacing-lg);
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

/* Company Overview Section */
.company-overview {
    background: var(--gray-100);
}

.company-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.company-content h2 {
    font-size: clamp(2rem, 4vw, 2.5rem);
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.lead {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
    color: var(--gray-700);
}

.company-stats {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.stat-number {
    font-family: var(--font-secondary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.company-actions {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.company-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.company-team-img {
    width: 100%;
    max-width: 500px;
    height: 400px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.company-team-img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-premium);
}

.image-placeholder {
    width: 100%;
    height: 400px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    text-align: center;
}

.image-placeholder i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.8;
}

.image-placeholder p {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
}

/* About Page Styles */
.page-header {
    padding-top: 120px;
    background: var(--gradient-primary);
    color: var(--white-color);
    min-height: 40vh;
    display: flex;
    align-items: center;
}

.page-header .section-subtitle {
    color: rgba(255, 255, 255, 0.9);
}

.page-title-white {
    color: var(--white-color) !important;
}

/* Company Stats Section */
.company-stats-section {
    background: var(--white-color);
    margin-top: -60px;
    position: relative;
    z-index: 2;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
}

.stat-card {
    background: var(--white-color);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-premium);
}

.stat-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon i {
    font-size: 1.5rem;
    color: var(--white-color);
}

.stat-number {
    font-family: var(--font-secondary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Vision & Mission Section */
.vision-mission-section {
    background: var(--white-color);
}

.vision-mission-centered {
    display: flex;
    justify-content: center;
    gap: var(--spacing-3xl);
    max-width: 900px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.vision-mission-centered .vm-item {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

/* Company Background Section */
.company-background-section {
    background: var(--gray-100);
}

.company-background-text h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.company-background-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* About Content - Legacy Support */
.about-content {
    background: var(--gray-100);
}

.about-section {
    margin-bottom: var(--spacing-2xl);
}

.about-section h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.vision-mission {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.vm-item {
    display: flex;
    gap: var(--spacing-lg);
    align-items: flex-start;
}

.vm-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.vm-icon i {
    color: var(--white-color);
    font-size: 1.2rem;
}

.vm-content h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
}

.about-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-team-img {
    width: 100%;
    max-width: 450px;
    height: 400px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.about-team-img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-premium);
}

.about-image-placeholder {
    width: 100%;
    height: 400px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    text-align: center;
}

.about-image-placeholder i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.8;
}

.about-image-placeholder p {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.9;
}

/* Timeline Section */
.timeline-section {
    background: var(--white-color);
}

.timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--gradient-secondary);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-3xl);
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.timeline-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-year {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-weight: 700;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
    box-shadow: var(--shadow-md);
}

.timeline-content {
    flex: 1;
    background: var(--white-color);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin: 0 var(--spacing-xl);
    border: 1px solid var(--gray-200);
    position: relative;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    transform: translateY(-50%);
}

.timeline-item:nth-child(odd) .timeline-content::before {
    left: -20px;
    border-right-color: var(--white-color);
}

.timeline-item:nth-child(even) .timeline-content::before {
    right: -20px;
    border-left-color: var(--white-color);
}

.timeline-content h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
}

.timeline-content p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.6;
}

/* Founder Section */
.founder-section {
    background: var(--gray-100);
}

.founder-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.founder-img {
    width: 100%;
    max-width: 350px;
    height: 450px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.founder-img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-premium);
}

.founder-img-placeholder {
    width: 300px;
    height: 400px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.founder-img-placeholder i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.8;
}

.founder-img-placeholder p {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
}

.founder-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.founder-content h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.founder-content h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
}

.founder-achievements {
    margin-top: var(--spacing-xl);
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    color: var(--gray-700);
}

.achievement-item i {
    color: var(--secondary-color);
    font-size: 1.2rem;
    width: 20px;
}

/* Compliance Section */
.compliance-section {
    background: var(--white-color);
}

.compliance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
}

.compliance-card {
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: var(--white-color);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
}

.compliance-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-premium);
}

.compliance-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    box-shadow: var(--shadow-sm);
}

.compliance-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.compliance-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.compliance-icon i {
    font-size: 1.8rem;
    color: var(--white-color);
}

.compliance-card h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.2rem;
}

.compliance-card p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.6;
}

/* Featured Projects Carousel */
.featured-projects {
    background: var(--white-color);
}

.projects-carousel {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-2xl);
}

.project-slide {
    display: none;
    grid-template-columns: 1fr 1fr;
    min-height: 400px;
    background: var(--white-color);
}

.project-slide.active {
    display: grid;
}

.project-image {
    position: relative;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.project-slide:hover .project-image img {
    transform: scale(1.05);
}

.project-content {
    padding: var(--spacing-3xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.project-category {
    font-size: 0.9rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.project-content h3 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    line-height: 1.3;
}

.project-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.project-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.project-features span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    color: var(--gray-600);
}

.project-features i {
    color: var(--secondary-color);
}

.carousel-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-xl);
}

.carousel-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--white-color);
    border: 2px solid var(--gray-300);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.carousel-btn:hover {
    background: var(--primary-color);
    color: var(--white-color);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.carousel-indicators {
    display: flex;
    gap: var(--spacing-sm);
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-300);
    border: none;
    cursor: pointer;
    transition: var(--transition-normal);
}

.indicator.active,
.indicator:hover {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Testimonials Section */
.testimonials {
    background: var(--gray-100);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
}

.testimonial-card {
    background: var(--white-color);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-premium);
}

.testimonial-content {
    margin-bottom: var(--spacing-xl);
}

.quote-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.quote-icon i {
    color: var(--white-color);
    font-size: 1.2rem;
}

.testimonial-content p {
    font-style: italic;
    color: var(--gray-700);
    line-height: 1.7;
    margin: 0;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.author-info h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.author-info span {
    color: var(--gray-500);
    font-size: 0.9rem;
}

.testimonial-rating {
    display: flex;
    gap: 2px;
}

.testimonial-rating i {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Call to Action Section */
.cta-section {
    background: var(--gradient-primary);
    color: var(--white-color);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: clamp(2rem, 4vw, 2.5rem);
    margin-bottom: var(--spacing-lg);
    color: var(--white-color);
}

.cta-content p,
.cta-description {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-2xl);
    opacity: 0.85;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .project-slide {
        grid-template-columns: 1fr;
        min-height: auto;
    }

    .project-content {
        padding: var(--spacing-xl);
    }

    .project-content h3 {
        font-size: 1.4rem;
    }

    .project-features {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .carousel-controls {
        gap: var(--spacing-lg);
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-author {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 280px;
    }
}

/* Portfolio Filters */
.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-2xl);
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--gray-300);
    color: var(--gray-600);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
    z-index: -1;
}

.filter-btn:hover,
.filter-btn.active {
    color: var(--white-color);
    border-color: var(--primary-color);
}

.filter-btn:hover::before,
.filter-btn.active::before {
    left: 0;
}

/* Portfolio Section */
.portfolio-section {
    background: var(--gray-100);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.portfolio-grid .portfolio-item {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.4s ease, transform 0.6s ease;
}

.portfolio-item {
    background: var(--white-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.4s ease, transform 0.6s ease;
    cursor: pointer;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

.portfolio-item.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.portfolio-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-premium);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.05);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-hero);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-actions {
    display: flex;
    gap: var(--spacing-md);
}

.portfolio-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.portfolio-btn:hover {
    background: var(--white-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

.portfolio-info {
    padding: var(--spacing-xl);
}

.portfolio-category {
    font-size: 0.85rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.portfolio-info h3 {
    font-size: 1.3rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    line-height: 1.3;
}

.portfolio-info p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.portfolio-meta {
    display: flex;
    gap: var(--spacing-lg);
    font-size: 0.9rem;
    color: var(--gray-500);
}

.portfolio-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.portfolio-meta i {
    color: var(--secondary-color);
}

/* Enhanced Lightbox Modal */
.lightbox {
    display: none;
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    opacity: 0;
    transition: var(--transition-normal);
}

.lightbox.active {
    opacity: 1;
}

.lightbox-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
}

.lightbox-content {
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    background: var(--white-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transform: scale(0.8);
    transition: var(--transition-normal);
}

.lightbox.active .lightbox-content {
    transform: scale(1);
}

.lightbox-content img {
    width: 60%;
    height: auto;
    object-fit: cover;
    max-height: 80vh;
}

.lightbox-info {
    width: 40%;
    padding: var(--spacing-2xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.lightbox-info h3 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.lightbox-info p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.lightbox-close,
.lightbox-prev,
.lightbox-next {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: var(--white-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.lightbox-close {
    top: var(--spacing-lg);
    right: var(--spacing-lg);
}

.lightbox-prev {
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
}

.lightbox-next {
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
}

.lightbox-close:hover,
.lightbox-prev:hover,
.lightbox-next:hover {
    background: var(--white-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

.lightbox-prev:hover {
    transform: translateY(-50%) scale(1.1);
}

.lightbox-next:hover {
    transform: translateY(-50%) scale(1.1);
}

.lightbox-counter {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: var(--white-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

/* Mobile Lightbox */
@media (max-width: 768px) {
    .lightbox-content {
        flex-direction: column;
        max-width: 95vw;
        max-height: 95vh;
    }

    .lightbox-content img {
        width: 100%;
        height: 60%;
    }

    .lightbox-info {
        width: 100%;
        height: 40%;
        padding: var(--spacing-lg);
    }

    .lightbox-info h3 {
        font-size: 1.4rem;
    }

    .lightbox-prev,
    .lightbox-next {
        display: none;
    }
}

/* Contact Page - Redesigned */
.contact-section {
    background: var(--gray-100);
}

.contact-layout {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3xl);
}

.contact-form-section {
    background: var(--white-color);
    padding: var(--spacing-3xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    order: 1;
}

.form-header {
    margin-bottom: var(--spacing-2xl);
    text-align: center;
    border-bottom: 2px solid var(--gray-200);
    padding-bottom: var(--spacing-lg);
}

.form-header h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: 1.8rem;
}

.form-header h2 i {
    color: var(--secondary-color);
}

.form-header p {
    color: var(--gray-600);
    margin: 0;
    font-size: 1.1rem;
}

.premium-form {
    position: relative;
}

.form-submit-container {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--dark-color);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition-normal);
    background: var(--white-color);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-error {
    display: block;
    color: var(--accent-color);
    font-size: 0.85rem;
    margin-top: var(--spacing-xs);
    opacity: 0;
    transition: var(--transition-normal);
}

.form-error.show {
    opacity: 1;
}

.form-status {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    text-align: center;
    font-weight: 500;
    opacity: 0;
    transition: var(--transition-normal);
}

.form-status.show {
    opacity: 1;
}

.form-status.success {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.form-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Contact Info Section - Redesigned */
.contact-info-section {
    order: 2;
}

.contact-info-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.contact-info-header h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: 1.8rem;
}

.contact-info-header h2 i {
    color: var(--secondary-color);
}

.contact-intro {
    color: var(--gray-600);
    line-height: 1.7;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Methods Grid */
.contact-methods-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.contact-method-card {
    background: var(--white-color);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-method-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.contact-method-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-premium);
}

.contact-method-card:hover::before {
    transform: scaleX(1);
}

.contact-method-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: var(--transition-normal);
}

.contact-method-icon i {
    color: var(--white-color);
    font-size: 1.8rem;
}

.contact-method-card:hover .contact-method-icon {
    transform: scale(1.1) rotate(5deg);
}

.contact-method-info h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
    font-size: 1.2rem;
    font-weight: 600;
}

.contact-method-info p {
    color: var(--gray-500);
    font-size: 0.95rem;
    margin-bottom: var(--spacing-md);
}

.contact-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--dark-color);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-normal);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--gray-100);
}

.contact-link:hover {
    color: var(--primary-color);
    background: rgba(26, 54, 93, 0.1);
    transform: translateY(-1px);
}

.contact-link i {
    color: var(--secondary-color);
}

.contact-address {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--dark-color);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--gray-100);
}

.contact-address i {
    color: var(--secondary-color);
}

/* Contact Actions */
.contact-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
    flex-wrap: wrap;
}

.btn-whatsapp-premium {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-md);
    background: #25D366;
    color: var(--white-color);
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-whatsapp-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn-whatsapp-premium:hover::before {
    left: 100%;
}

.btn-whatsapp-premium:hover {
    background: #1EBE57;
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    color: var(--white-color);
}

.btn-whatsapp-premium i {
    font-size: 1.3rem;
}

.btn-call-premium {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-md);
    background: var(--gradient-primary);
    color: var(--white-color);
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-call-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn-call-premium:hover::before {
    left: 100%;
}

.btn-call-premium:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    color: var(--white-color);
}

.btn-call-premium i {
    font-size: 1.3rem;
}

/* Map Section */
.map-section {
    background: var(--white-color);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.map-section h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.map-section h4 i {
    color: var(--secondary-color);
}

.map-container {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.map-container iframe {
    border-radius: var(--radius-lg);
    width: 100%;
    transition: var(--transition-normal);
}

.map-container:hover iframe {
    transform: scale(1.02);
}

/* Contact Page Mobile */
@media (max-width: 768px) {
    .contact-layout {
        gap: var(--spacing-2xl);
    }

    .contact-form-section {
        padding: var(--spacing-xl);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .contact-methods-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .form-submit-container {
        justify-content: center;
    }

    .contact-method-card {
        padding: var(--spacing-lg);
    }

    .contact-method-icon {
        width: 60px;
        height: 60px;
    }

    .contact-method-icon i {
        font-size: 1.5rem;
    }

    .contact-actions {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .btn-whatsapp-premium,
    .btn-call-premium {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .map-section {
        padding: var(--spacing-lg);
    }

    .form-header h2,
    .contact-info-header h2,
    .map-section h4 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}


/* Footer */
footer {
    background: var(--dark-color);
    color: var(--white-color);
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
}

.client-logos img {
    height: 40px;
    margin: 0 10px;
    filter: grayscale(100%) invert(100%);
}

.social-icons a {
    color: var(--white-color);
    margin: 0 10px;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: var(--secondary-color);
}

/* WhatsApp Button */
.whatsapp-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #25D366;
    color: var(--white-color);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.whatsapp-btn:hover {
    transform: scale(1.1);
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .nav-links {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        padding: var(--spacing-xl) 0;
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
        z-index: 999;
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links li {
        margin: var(--spacing-sm) 0;
        width: 100%;
        text-align: center;
    }

    .nav-links a {
        display: block;
        padding: var(--spacing-md) var(--spacing-lg);
        width: 100%;
    }

    /* Hero Mobile Adjustments */
    .hero-content {
        padding: 0 var(--spacing-md);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .btn {
        width: 100%;
        max-width: 280px;
    }

    /* Grid Mobile Adjustments */
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .company-stats {
        justify-content: center;
        gap: var(--spacing-lg);
    }

    .stat-item {
        min-width: 100px;
    }

    .company-actions {
        flex-direction: column;
        align-items: center;
    }

    .company-actions .btn {
        width: 100%;
        max-width: 250px;
    }

    /* Service Cards Mobile */
    .service-card {
        padding: var(--spacing-xl);
    }

    .service-icon {
        width: 60px;
        height: 60px;
    }

    .service-icon i {
        font-size: 1.5rem;
    }

    /* Image Placeholder Mobile */
    .image-placeholder {
        height: 300px;
    }

    .image-placeholder i {
        font-size: 3rem;
    }

    /* Footer Mobile */
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-xl);
    }

    .footer-section {
        margin-bottom: 0;
    }
}

/* Tablet Adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
    .container {
        padding: 0 var(--spacing-lg);
    }

    .grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }

    .company-stats {
        gap: var(--spacing-lg);
    }

    .contact-methods-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

/* Large Screen Adjustments */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .hero-content {
        max-width: 900px;
    }

    .section-header h2 {
        font-size: 3.5rem;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-bg-elements::before,
    .hero-bg-elements::after {
        animation: none;
    }

    .scroll-indicator {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #FFD700;
        --dark-color: #000000;
        --light-color: #FFFFFF;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .whatsapp-btn,
    .scroll-indicator,
    .hero-bg-elements {
        display: none;
    }

    .hero {
        min-height: auto;
        padding: var(--spacing-xl) 0;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3 {
        page-break-after: avoid;
    }
}

/* Skip Link for Accessibility */
.skip-link:focus {
    position: absolute !important;
    top: 6px !important;
    left: 6px !important;
    background: var(--primary-color) !important;
    color: var(--white-color) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    text-decoration: none !important;
    border-radius: var(--radius-md) !important;
    z-index: 10000 !important;
    font-weight: 600 !important;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Lazy Loading Images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Enhanced Focus Styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Improved Button States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: var(--shadow-md) !important;
}

/* Enhanced Mobile Experience */
@media (max-width: 480px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
        --spacing-3xl: 3rem;
    }

    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-content {
        padding: 0 var(--spacing-sm);
    }

    .section {
        padding: var(--spacing-2xl) 0;
    }

    .service-card,
    .testimonial-card,
    .compliance-card {
        padding: var(--spacing-lg);
    }

    .project-content {
        padding: var(--spacing-lg);
    }

    .contact-form-container {
        padding: var(--spacing-lg);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .btn {
        min-width: auto;
        width: 100%;
    }

    .hero-buttons .btn,
    .company-actions .btn,
    .cta-buttons .btn {
        max-width: none;
    }
}

/* Performance Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
}

img {
    max-width: 100%;
    height: auto;
}

/* Smooth scrolling for all browsers */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

/* Enhanced WhatsApp Button */
.whatsapp-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #25D366;
    color: var(--white-color);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    z-index: 1000;
    text-decoration: none;
}

.whatsapp-btn:hover {
    transform: scale(1.1);
    background: #1EBE57;
    color: var(--white-color);
}

.whatsapp-btn:focus {
    outline: 2px solid var(--white-color);
    outline-offset: 2px;
}

@media (max-width: 768px) {
    .whatsapp-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        bottom: 15px;
        right: 15px;
    }
}

/* Final Polish */
::selection {
    background: var(--primary-color);
    color: var(--white-color);
}

::-moz-selection {
    background: var(--primary-color);
    color: var(--white-color);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-200);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}
