// Premium main.js for Edge to Edge 1154cc

document.addEventListener('DOMContentLoaded', () => {
    // Mobile menu functionality
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const navbar = document.querySelector('.navbar');

    if (menuToggle && navLinks) {
        menuToggle.addEventListener('click', () => {
            menuToggle.classList.toggle('active');
            navLinks.classList.toggle('active');
            document.body.style.overflow = navLinks.classList.contains('active') ? 'hidden' : '';
        });

        // Close mobile menu when clicking on a link
        navLinks.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                menuToggle.classList.remove('active');
                navLinks.classList.remove('active');
                document.body.style.overflow = '';
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!menuToggle.contains(e.target) && !navLinks.contains(e.target)) {
                menuToggle.classList.remove('active');
                navLinks.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    // Navbar scroll effect
    let lastScrollTop = 0;
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide navbar on scroll down, show on scroll up
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        lastScrollTop = scrollTop;
    });

    // Set active navigation link
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    navLinks.querySelectorAll('a').forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });

    // Portfolio Filter Functionality
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    if (filterBtns.length > 0) {
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // Remove active class from all buttons
                filterBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                btn.classList.add('active');

                const filter = btn.getAttribute('data-filter');

                // Create arrays for matching and non-matching items
                const matchingItems = [];
                const nonMatchingItems = [];

                portfolioItems.forEach(item => {
                    const category = item.getAttribute('data-category');
                    if (filter === 'all' || category === filter) {
                        matchingItems.push(item);
                    } else {
                        nonMatchingItems.push(item);
                    }
                });

                // Hide all items first
                portfolioItems.forEach(item => {
                    item.classList.remove('show');
                });

                // Show matching items first with staggered animation
                matchingItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('show');
                        item.style.order = index;
                    }, index * 100);
                });

                // Show non-matching items after with lower priority
                nonMatchingItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('show');
                        item.style.order = matchingItems.length + index;
                        item.style.opacity = '0.4';
                    }, (matchingItems.length * 100) + (index * 50));
                });

                // Reset opacity for matching items
                matchingItems.forEach(item => {
                    item.style.opacity = '1';
                });
            });
        });

        // Show all items initially
        setTimeout(() => {
            portfolioItems.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('show');
                }, index * 100);
            });
        }, 300);
    }

    // Enhanced Lightbox functionality
    const modal = document.getElementById('lightbox-modal');
    const modalImg = document.getElementById('lightbox-img');
    const lightboxTitle = document.getElementById('lightbox-title');
    const lightboxDescription = document.getElementById('lightbox-description');
    const lightboxMeta = document.getElementById('lightbox-meta');
    const lightboxCurrent = document.getElementById('lightbox-current');
    const lightboxTotal = document.getElementById('lightbox-total');
    const closeBtn = document.querySelector('.lightbox-close');
    const prevBtn = document.querySelector('.lightbox-prev');
    const nextBtn = document.querySelector('.lightbox-next');

    let currentImageIndex = 0;
    let visibleItems = [];

    function updateVisibleItems() {
        visibleItems = Array.from(portfolioItems).filter(item => item.classList.contains('show'));
        if (lightboxTotal) {
            lightboxTotal.textContent = visibleItems.length;
        }
    }

    function openLightbox(index) {
        if (!modal || visibleItems.length === 0) return;

        currentImageIndex = index;
        const item = visibleItems[currentImageIndex];
        const img = item.querySelector('img');
        const title = item.querySelector('h3').textContent;
        const description = item.querySelector('.portfolio-info p').textContent;
        const category = item.querySelector('.portfolio-category').textContent;
        const meta = item.querySelector('.portfolio-meta').innerHTML;

        modalImg.src = img.src;
        modalImg.alt = img.alt;
        if (lightboxTitle) lightboxTitle.textContent = title;
        if (lightboxDescription) lightboxDescription.textContent = description;
        if (lightboxMeta) lightboxMeta.innerHTML = `<div class="portfolio-category">${category}</div>${meta}`;
        if (lightboxCurrent) lightboxCurrent.textContent = currentImageIndex + 1;

        modal.style.display = 'block';
        setTimeout(() => modal.classList.add('active'), 10);
        document.body.style.overflow = 'hidden';
    }

    function closeLightbox() {
        if (!modal) return;

        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }

    function showPrevImage() {
        currentImageIndex = (currentImageIndex - 1 + visibleItems.length) % visibleItems.length;
        openLightbox(currentImageIndex);
    }

    function showNextImage() {
        currentImageIndex = (currentImageIndex + 1) % visibleItems.length;
        openLightbox(currentImageIndex);
    }

    // Portfolio item click handlers
    portfolioItems.forEach((item) => {
        const viewBtn = item.querySelector('[data-action="view"]');
        if (viewBtn) {
            viewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                updateVisibleItems();
                const visibleIndex = visibleItems.indexOf(item);
                if (visibleIndex !== -1) {
                    openLightbox(visibleIndex);
                }
            });
        }
    });

    // Lightbox controls
    if (closeBtn) {
        closeBtn.addEventListener('click', closeLightbox);
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', showPrevImage);
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', showNextImage);
    }

    // Close lightbox on background click
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeLightbox();
            }
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (modal && modal.classList.contains('active')) {
            switch(e.key) {
                case 'Escape':
                    closeLightbox();
                    break;
                case 'ArrowLeft':
                    showPrevImage();
                    break;
                case 'ArrowRight':
                    showNextImage();
                    break;
            }
        }
    });


    // Smooth scrolling for anchor links and scroll indicator
    document.querySelectorAll('a[href^="#"], .scroll-indicator').forEach(element => {
        element.addEventListener('click', function (e) {
            e.preventDefault();

            let targetId = this.getAttribute('href');
            if (!targetId || targetId === '#') {
                // Scroll to next section for scroll indicator
                const nextSection = document.querySelector('.services-overview') || document.querySelector('.section');
                if (nextSection) {
                    nextSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                return;
            }

            const target = document.querySelector(targetId);
            if (target) {
                const offsetTop = target.offsetTop - 100; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Animate statistics counters
                if (entry.target.classList.contains('stat-number') || entry.target.querySelector('.stat-number[data-target]')) {
                    const statNumber = entry.target.classList.contains('stat-number') ?
                        entry.target : entry.target.querySelector('.stat-number[data-target]');
                    if (statNumber && statNumber.dataset.target) {
                        animateCounter(statNumber);
                    }
                }
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .company-content, .company-image, .stat-item, .stat-card, .timeline-item, .compliance-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });

    // Counter animation function
    function animateCounter(element) {
        const target = element.dataset.target;
        if (!target || isNaN(target)) return;

        const targetNum = parseInt(target);
        const duration = 2000;
        const increment = targetNum / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= targetNum) {
                current = targetNum;
                clearInterval(timer);
            }

            element.textContent = Math.floor(current) + (targetNum > 99 ? '+' : '');
        }, 16);
    }

    // Timeline stagger animation
    const timelineItems = document.querySelectorAll('.timeline-item');
    if (timelineItems.length > 0) {
        const timelineObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, index * 200);
                }
            });
        }, { threshold: 0.2 });

        timelineItems.forEach(item => {
            timelineObserver.observe(item);
        });
    }

    // Featured Projects Carousel
    const carousel = {
        slides: document.querySelectorAll('.project-slide'),
        indicators: document.querySelectorAll('.indicator'),
        prevBtn: document.querySelector('.prev-btn'),
        nextBtn: document.querySelector('.next-btn'),
        currentSlide: 0,

        init() {
            if (this.slides.length === 0) return;

            this.showSlide(0);
            this.bindEvents();
            this.autoPlay();
        },

        showSlide(index) {
            // Hide all slides
            this.slides.forEach(slide => slide.classList.remove('active'));
            this.indicators.forEach(indicator => indicator.classList.remove('active'));

            // Show current slide
            if (this.slides[index]) {
                this.slides[index].classList.add('active');
            }
            if (this.indicators[index]) {
                this.indicators[index].classList.add('active');
            }

            this.currentSlide = index;
        },

        nextSlide() {
            const next = (this.currentSlide + 1) % this.slides.length;
            this.showSlide(next);
        },

        prevSlide() {
            const prev = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
            this.showSlide(prev);
        },

        bindEvents() {
            if (this.nextBtn) {
                this.nextBtn.addEventListener('click', () => this.nextSlide());
            }

            if (this.prevBtn) {
                this.prevBtn.addEventListener('click', () => this.prevSlide());
            }

            this.indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => this.showSlide(index));
            });

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') this.prevSlide();
                if (e.key === 'ArrowRight') this.nextSlide();
            });
        },

        autoPlay() {
            setInterval(() => {
                this.nextSlide();
            }, 8000); // Change slide every 8 seconds
        }
    };

    carousel.init();

    // Advanced Form Validation and Submission
    const form = document.getElementById('contact-form');
    const formStatus = document.getElementById('form-status');

    if (form) {
        const formFields = {
            name: form.querySelector('#name'),
            email: form.querySelector('#email'),
            phone: form.querySelector('#phone'),
            message: form.querySelector('#message')
        };

        const formErrors = {
            name: form.querySelector('#name-error'),
            email: form.querySelector('#email-error'),
            phone: form.querySelector('#phone-error'),
            message: form.querySelector('#message-error')
        };

        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        // Real-time validation
        Object.keys(formFields).forEach(fieldName => {
            const field = formFields[fieldName];
            const errorElement = formErrors[fieldName];

            if (field && errorElement) {
                field.addEventListener('blur', () => validateField(fieldName, field, errorElement));
                field.addEventListener('input', () => clearError(errorElement));
            }
        });

        function validateField(fieldName, field, errorElement) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';

            switch (fieldName) {
                case 'name':
                    if (!value) {
                        errorMessage = 'Name is required';
                        isValid = false;
                    } else if (value.length < 2) {
                        errorMessage = 'Name must be at least 2 characters';
                        isValid = false;
                    }
                    break;

                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!value) {
                        errorMessage = 'Email is required';
                        isValid = false;
                    } else if (!emailRegex.test(value)) {
                        errorMessage = 'Please enter a valid email address';
                        isValid = false;
                    }
                    break;

                case 'phone':
                    if (value && !/^[\d\s\-\+\(\)]+$/.test(value)) {
                        errorMessage = 'Please enter a valid phone number';
                        isValid = false;
                    }
                    break;

                case 'message':
                    if (!value) {
                        errorMessage = 'Message is required';
                        isValid = false;
                    } else if (value.length < 10) {
                        errorMessage = 'Message must be at least 10 characters';
                        isValid = false;
                    }
                    break;
            }

            if (!isValid) {
                showError(errorElement, errorMessage);
                field.style.borderColor = 'var(--accent-color)';
            } else {
                clearError(errorElement);
                field.style.borderColor = 'var(--gray-300)';
            }

            return isValid;
        }

        function showError(errorElement, message) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        function clearError(errorElement) {
            errorElement.textContent = '';
            errorElement.classList.remove('show');
        }

        function validateForm() {
            let isFormValid = true;

            Object.keys(formFields).forEach(fieldName => {
                const field = formFields[fieldName];
                const errorElement = formErrors[fieldName];

                if (field && errorElement) {
                    const isFieldValid = validateField(fieldName, field, errorElement);
                    if (!isFieldValid) {
                        isFormValid = false;
                    }
                }
            });

            return isFormValid;
        }

        function showFormStatus(message, type) {
            formStatus.textContent = message;
            formStatus.className = `form-status show ${type}`;

            setTimeout(() => {
                formStatus.classList.remove('show');
            }, 5000);
        }

        function setLoadingState(loading) {
            if (loading) {
                submitBtn.disabled = true;
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-flex';
                submitBtn.classList.add('loading');
            } else {
                submitBtn.disabled = false;
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                submitBtn.classList.remove('loading');
            }
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!validateForm()) {
                showFormStatus('Please correct the errors above', 'error');
                return;
            }

            setLoadingState(true);
            const data = new FormData(form);

            try {
                // Simulate network delay for demo
                await new Promise(resolve => setTimeout(resolve, 1500));

                const response = await fetch(form.action, {
                    method: form.method,
                    body: data,
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    showFormStatus('Thank you! Your message has been sent successfully. We\'ll get back to you within 24 hours.', 'success');
                    form.reset();
                    // Clear all field styles
                    Object.values(formFields).forEach(field => {
                        if (field) field.style.borderColor = 'var(--gray-300)';
                    });
                } else {
                    const responseData = await response.json();
                    if (Object.hasOwn(responseData, 'errors')) {
                        showFormStatus(responseData["errors"].map(error => error["message"]).join(", "), 'error');
                    } else {
                        showFormStatus('There was a problem sending your message. Please try again.', 'error');
                    }
                }
            } catch (error) {
                showFormStatus('There was a problem sending your message. Please check your connection and try again.', 'error');
            } finally {
                setLoadingState(false);
            }
        });
    }

    // Performance Optimizations

    // Lazy loading for images
    const lazyImages = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    lazyImages.forEach(img => imageObserver.observe(img));

    // Preload critical resources
    function preloadCriticalResources() {
        const criticalImages = [
            'images/hero-bg.jpg',
            'images/chetty-house.jpg',
            'images/pmu-building.jpg'
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    // Call preload function
    preloadCriticalResources();

    // Debounced scroll handler for better performance
    let scrollTimeout;
    function debounceScroll(func, wait) {
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(scrollTimeout);
                func(...args);
            };
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(later, wait);
        };
    }

    // Optimized scroll handler
    const optimizedScrollHandler = debounceScroll(() => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Update navbar
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }, 10);

    window.addEventListener('scroll', optimizedScrollHandler, { passive: true });

    // Service Worker registration for caching (if available)
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered: ', registration);
                })
                .catch(registrationError => {
                    console.log('SW registration failed: ', registrationError);
                });
        });
    }

    // Error handling for missing images
    document.querySelectorAll('img').forEach(img => {
        img.addEventListener('error', function() {
            this.style.display = 'none';
            console.warn('Failed to load image:', this.src);
        });
    });

    // Accessibility improvements

    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--primary-color);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        transition: top 0.3s;
    `;

    skipLink.addEventListener('focus', () => {
        skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
        skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);

    // Add main id to main element if not present
    const mainElement = document.querySelector('main');
    if (mainElement && !mainElement.id) {
        mainElement.id = 'main';
    }

    // Focus management for modal
    function trapFocus(element) {
        const focusableElements = element.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstFocusableElement = focusableElements[0];
        const lastFocusableElement = focusableElements[focusableElements.length - 1];

        element.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusableElement) {
                        lastFocusableElement.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusableElement) {
                        firstFocusableElement.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }

    // Apply focus trap to lightbox when active
    if (modal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (modal.classList.contains('active')) {
                        trapFocus(modal);
                        modal.querySelector('.lightbox-close')?.focus();
                    }
                }
            });
        });

        observer.observe(modal, { attributes: true });
    }

    console.log('🏗️ Edge to Edge 1154cc - Premium website loaded successfully!');
});
